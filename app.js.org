const express = require("express");
const bodyParser = require("body-parser");
const Sequelize = require("sequelize");
const cors = require("cors");
const { response } = require("express");

const app = express();

app.use(cors());

const PORT = 8000;

const sql58 = `
SELECT DISTINCT 
 	dbo.GetEmployeeNickOrFormatNameByID(cus.InCharge,1,'A') AS InChargeOfCustomer , 
    rtrim(ja.Team) AS JobTeam , 
    rtrim(ja.CustomerID) AS CustomerID , 
	case when cus.CompanyName_A1<>'' then rtrim(cus.CompanyName_A1)+' '+cus.CompanyName_A2 when cus.CompanyName_B1 <>'' then rtrim(cus.CompanyName_B1)+' '+cus.CompanyName_B2 else rtrim(cus.CompanyName_C1)+' '+cus.CompanyName_C2 end AS CustomerName , ja.JobAssignmentID AS JobAssignmentID , 
	CASE WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasia WHERE sjasia.JobStatusCode='N_LOCK_CNR' AND sjasia.CompanyID = ja.CompanyID AND sjasia.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		  THEN 
		   'N_LOCK_CNR'
		WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasia WHERE sjasia.JobStatusCode='LOCKED' AND sjasia.CompanyID = ja.CompanyID AND sjasia.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		  THEN 
		   'LOCKED'
		  WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasib WHERE sjasib.JobStatusCode='LOCKED_F' AND sjasib.CompanyID = ja.CompanyID AND sjasib.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		  THEN
		   'LOCKED_F'
		  WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasic WHERE sjasic.JobStatusCode='CC' AND sjasic.CompanyID = ja.CompanyID AND sjasic.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		  THEN
		   'CC'
		  ELSE
			 (select top 1 JobStatusCode from JobAssignmentStatusInfo sjasi where sjasi.JobStatusCode <> '' AND sjasi.CompanyID = ja.CompanyID and sjasi.JobAssignmentID = ja.JobAssignmentID order by sjasi.Date_Update DESC
			)  
	end AS CurrentJobStatusCode , 
	rtrim(jasi.JobStatusCode) AS JobStatusCode , 
	cast(ja.CompleteDate as date ) AS JobCompleteDate , 
	cast(jasi.Date_Update as date ) AS JobStatusUpdatedOn , 
	dbo.GetEmployeeNickOrFormatNameByID(jasi.EmployeeID,1,'A') AS JobStatusUpdateBy , 
	cast(ja.InternalDeadLine as date ) AS JobInternalDeadLine , 
	case dbo.RBCheckCorrectJobStatusOrder( ja.CompanyID,ja.JobAssignmentID ) when 1 then 'No' else 'Yes' end AS JobStatusOrderCheck , 
	( select COUNT(*) from JobAssignmentStatusInfo jasic where jasic.JobAssignmentID = jasi.JobAssignmentID and jasic.JobStatusCode = jasi.JobStatusCode ) AS JobStatusCodeCount , 
	dbo.GetEmployeeNickOrFormatNameByID(ja.Assistant,1,'A') AS JobAssistantName , 
	dbo.GetEmployeeNickOrFormatNameByID(ja.Incharge,1,'A') AS JobIncharge , 
	isnull(jis.NumberOfInvoice, 0) AS NumberOfInvoice , 
	(select replace(replace(( select distinct '~' + rtrim(ih.ConfirmedInvoiceNumber) + '~' from InvoiceHead ih, InvoiceDetail id where ih.CompanyID = id.CompanyID and ih.UniqueID = id.UniqueID and ih.Status = 1 and id.SourceType = 'J' and id.CompanyID = ja.CompanyID and id.SourceID = ja.JobAssignmentID and isnull( ih.ConfirmedInvoiceNumber, '' ) <> '' order by '~' + rtrim(ih.ConfirmedInvoiceNumber) + '~' for xml path('')),'~~',', '),'~','')) AS JobAssignmentInvoiceList , 
	cast(jis.LastInvoicedDate as date) AS LastInvoicedDate , 
	jis.TotalInvoicedAmount AS TotalInvoicedAmount , 
	( select count(*) from JobAssignmentStatusInfo ssjasi where ssjasi.CompanyID = ja.CompanyID and ssjasi.JobAssignmentID = ja.JobAssignmentID and ssjasi.JobStatusCode = 'DOA' ) AS JobStatus_DOA_Count FROM JobAssignment ja 
LEFT JOIN JobAssignmentMember jam ON jam.CompanyID = ja.CompanyID AND jam.JobAssignmentID = ja.JobAssignmentID 
LEFT JOIN Employee e ON jam.CompanyID = e.CompanyID AND jam.EmployeeID = e.EmployeeID 
LEFT JOIN EmployeeJOb jamj ON e.CompanyID = jamj.CompanyID AND e.EmployeeID = jamj.EmployeeID and ( jamj.Effective = ( SELECT MAX( Effective ) from EmployeeJob jamjs where jamjs.CompanyID = jamj.CompanyID AND jamjs.EmployeeID = jamj.EmployeeID and jamjs.Effective <= GETDATE() )) 
LEFT JOIN TimeCost jamtc ON jamj.CompanyID = jamtc.CompanyID and jamj.TimeCostID = jamtc.TimeCostID and ( jamtc.Effective = ( SELECT MAX( Effective ) from TimeCost jamtcs where jamtcs.CompanyID = jamtc.CompanyID AND jamtcs.TimeCostID = jamtc.TimeCostID and jamtcs.Effective <= GETDATE() )) 
LEFT JOIN Contact c ON jam.CompanyID = c.CompanyID AND e.EmployeeID = c.MasterID AND c.MasterType = 'EMP' AND c.SeqID = 1 
LEFT JOIN Customer cus ON ja.CompanyID = cus.CompanyID AND cus.CustomerID = ja.CustomerID 
LEFT JOIN Contact cc ON ja.CompanyID = cc.CompanyID AND ja.CustomerID = cc.MasterID AND cc.MasterType = 'CUG' AND cc.SeqID = 1 
LEFT JOIN JobAssignmentStatusInfo jasi ON ja.CompanyID = jasi.CompanyID and ja.JobAssignmentID = jasi.JobAssignmentID 
left join Intermediary ity on ja.CompanyID = ity.CompanyID and ja.IntermediaryID = ity.IntermediaryID 
left join CompanyType ct on ity.CompanyID = ct.CompanyID and ity.CompanyType = ct.CompanyType 
left join Contact city  on ity.CompanyID = city.CompanyID and ity.IntermediaryID = city.MasterID and city.MasterType = 'REF' 
left join Industry ind on ity.CompanyID = ind.CompanyID and ity.IndustryID = ind.IndustryID 
left join CodeDefinition cdtitle on city.CompanyID = cdtitle.CompanyID and city.TitleIDCode = cdtitle.Code and GroupCode = 'TITLE' 
LEFT JOIN JobAssignmentUserField jauf on ja.CompanyID = jauf.CompanyID and ja.JobAssignmentID = jauf.JobAssignmentID 
LEFT JOIN ( select ih.CompanyID, id1.SourceID, count( ih.ConfirmedInvoiceNumber ) NumberOfInvoice, max( ih.InvoiceDate ) LastInvoicedDate, sum(id1.TotalInvoicedAmount) TotalInvoicedAmount from InvoiceHead ih 
left join ( select CompanyID, SourceID, UniqueID, sum(Amount) TotalInvoicedAmount from InvoiceDetail where SourceType = 'J' group by CompanyID, SourceID, UniqueID ) id1 
	on ih.CompanyID = id1.CompanyID and ih.UniqueID = id1.UniqueID where ih.Status = 1 group by ih.CompanyID, id1.SourceID ) jis 
	on jis.CompanyID = ja.CompanyID and jis.SourceID = ja.JobAssignmentID WHERE 1=1
		AND (
				CASE WHEN (
						(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasia WHERE (sjasia.JobStatusCode='LOCKED' OR sjasia.JobStatusCode='N_LOCK_CNR') AND sjasia.CompanyID = ja.CompanyID AND sjasia.jobAssignmentID=ja.JobAssignmentID
						) IS NOT NULL)
					  THEN 
					   'LOCKED'
					  WHEN (
						(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasib WHERE sjasib.JobStatusCode='LOCKED_F' AND sjasib.CompanyID = ja.CompanyID AND sjasib.jobAssignmentID=ja.JobAssignmentID
						) IS NOT NULL) 
					  THEN
					   'LOCKED_F'
					  WHEN (
						(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasic WHERE sjasic.JobStatusCode='CC' 		AND sjasic.CompanyID = ja.CompanyID AND sjasic.jobAssignmentID=ja.JobAssignmentID
						) IS NOT NULL) 
					  THEN
					   'CC'
					  ELSE
						 (select top 1 JobStatusCode from JobAssignmentStatusInfo sjasi where sjasi.JobStatusCode <> '' AND sjasi.CompanyID = ja.CompanyID 	AND sjasi.JobAssignmentID = ja.JobAssignmentID order by sjasi.Date_Update DESC
						)  
				END		
			) <> 'LOCKED'
		AND (jasi.JobStatusCode = 'DOA') 
		AND 
			(
			(ja.BillingCode = 'ACC-ACC') OR (ja.BillingCode = 'AUD-IA') OR (ja.BillingCode = 'AUD-IAL') OR (ja.BillingCode like 'AUD-SA_') 
			OR (ja.BillingCode = 'AUD-SR') OR (ja.BillingCode = 'SPE-NT') OR (ja.BillingCode = 'SPE-DD') OR (ja.BillingCode = 'SPE-OS') OR (ja.BillingCode = 'SPE-PGT') 
			OR (ja.BillingCode = 'SPE-SR') OR (ja.BillingCode = 'SPE-ICR') OR (ja.BillingCode = 'SPE-MAR') OR (ja.BillingCode = 'SPE-PRC') 
			)
		AND (
			(ja.CustomerID NOT LIKE '%K') OR (ja.CustomerID LIKE '%SK')
			)
`;

const sql58_all = `
SELECT DISTINCT 
	dbo.GetEmployeeNickOrFormatNameByID(cus.InCharge,1,'A') AS InChargeOfCustomer , 
    rtrim(ja.Team) AS JobTeam , 
    rtrim(ja.CustomerID) AS CustomerID , 
	case when cus.CompanyName_A1<>'' then rtrim(cus.CompanyName_A1)+' '+cus.CompanyName_A2 when cus.CompanyName_B1 <>'' then rtrim(cus.CompanyName_B1)+' '+cus.CompanyName_B2 else rtrim(cus.CompanyName_C1)+' '+cus.CompanyName_C2 end AS CustomerName , ja.JobAssignmentID AS JobAssignmentID , 
	CASE 
		WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasia WHERE sjasia.JobStatusCode='N_LOCK_CNR' AND sjasia.CompanyID = ja.CompanyID AND sjasia.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		THEN 
			'N_LOCK_CNR'
		WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasia WHERE sjasia.JobStatusCode='LOCKED' AND sjasia.CompanyID = ja.CompanyID AND sjasia.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		THEN 
			'LOCKED'
		WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasib WHERE sjasib.JobStatusCode='LOCKED_F' AND sjasib.CompanyID = ja.CompanyID AND sjasib.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		THEN
			'LOCKED_F'
		WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasic WHERE sjasic.JobStatusCode='CC' AND sjasic.CompanyID = ja.CompanyID AND sjasic.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		THEN
			'CC'
		ELSE
			(select top 1 JobStatusCode from JobAssignmentStatusInfo sjasi where sjasi.JobStatusCode <> '' AND sjasi.CompanyID = ja.CompanyID and sjasi.JobAssignmentID = ja.JobAssignmentID order by sjasi.Date_Update DESC
			)  
	end AS CurrentJobStatusCode , 
	rtrim(jasi.JobStatusCode) AS JobStatusCode , 
	cast(ja.CompleteDate as date ) AS JobCompleteDate , 
	cast(jasi.Date_Update as date ) AS JobStatusUpdatedOn , 
	dbo.GetEmployeeNickOrFormatNameByID(jasi.EmployeeID,1,'A') AS JobStatusUpdateBy , 
	cast(ja.InternalDeadLine as date ) AS JobInternalDeadLine , 
	case dbo.RBCheckCorrectJobStatusOrder( ja.CompanyID,ja.JobAssignmentID ) when 1 then 'No' else 'Yes' end AS JobStatusOrderCheck , 
	( select COUNT(*) from JobAssignmentStatusInfo jasic where jasic.JobAssignmentID = jasi.JobAssignmentID and jasic.JobStatusCode = jasi.JobStatusCode ) AS JobStatusCodeCount , 
	dbo.GetEmployeeNickOrFormatNameByID(ja.Assistant,1,'A') AS JobAssistantName , 
	dbo.GetEmployeeNickOrFormatNameByID(ja.Incharge,1,'A') AS JobIncharge , 
	isnull(jis.NumberOfInvoice, 0) AS NumberOfInvoice , 
	(select replace(replace(( select distinct '~' + rtrim(ih.ConfirmedInvoiceNumber) + '~' from InvoiceHead ih, InvoiceDetail id where ih.CompanyID = id.CompanyID and ih.UniqueID = id.UniqueID and ih.Status = 1 and id.SourceType = 'J' and id.CompanyID = ja.CompanyID and id.SourceID = ja.JobAssignmentID and isnull( ih.ConfirmedInvoiceNumber, '' ) <> '' order by '~' + rtrim(ih.ConfirmedInvoiceNumber) + '~' for xml path('')),'~~',', '),'~','')) AS JobAssignmentInvoiceList , 
	cast(jis.LastInvoicedDate as date) AS LastInvoicedDate , 
	jis.TotalInvoicedAmount AS TotalInvoicedAmount , 
	( select count(*) from JobAssignmentStatusInfo ssjasi where ssjasi.CompanyID = ja.CompanyID and ssjasi.JobAssignmentID = ja.JobAssignmentID and ssjasi.JobStatusCode = 'DOA' ) AS JobStatus_DOA_Count FROM JobAssignment ja 
LEFT JOIN JobAssignmentMember jam ON jam.CompanyID = ja.CompanyID AND jam.JobAssignmentID = ja.JobAssignmentID 
LEFT JOIN Employee e ON jam.CompanyID = e.CompanyID AND jam.EmployeeID = e.EmployeeID 
LEFT JOIN EmployeeJOb jamj ON e.CompanyID = jamj.CompanyID AND e.EmployeeID = jamj.EmployeeID and ( jamj.Effective = ( SELECT MAX( Effective ) from EmployeeJob jamjs where jamjs.CompanyID = jamj.CompanyID AND jamjs.EmployeeID = jamj.EmployeeID and jamjs.Effective <= GETDATE() )) 
LEFT JOIN TimeCost jamtc ON jamj.CompanyID = jamtc.CompanyID and jamj.TimeCostID = jamtc.TimeCostID and ( jamtc.Effective = ( SELECT MAX( Effective ) from TimeCost jamtcs where jamtcs.CompanyID = jamtc.CompanyID AND jamtcs.TimeCostID = jamtc.TimeCostID and jamtcs.Effective <= GETDATE() )) 
LEFT JOIN Contact c ON jam.CompanyID = c.CompanyID AND e.EmployeeID = c.MasterID AND c.MasterType = 'EMP' AND c.SeqID = 1 
LEFT JOIN Customer cus ON ja.CompanyID = cus.CompanyID AND cus.CustomerID = ja.CustomerID 
LEFT JOIN Contact cc ON ja.CompanyID = cc.CompanyID AND ja.CustomerID = cc.MasterID AND cc.MasterType = 'CUG' AND cc.SeqID = 1 
LEFT JOIN JobAssignmentStatusInfo jasi ON ja.CompanyID = jasi.CompanyID and ja.JobAssignmentID = jasi.JobAssignmentID 
left join Intermediary ity on ja.CompanyID = ity.CompanyID and ja.IntermediaryID = ity.IntermediaryID 
left join CompanyType ct on ity.CompanyID = ct.CompanyID and ity.CompanyType = ct.CompanyType 
left join Contact city  on ity.CompanyID = city.CompanyID and ity.IntermediaryID = city.MasterID and city.MasterType = 'REF' 
left join Industry ind on ity.CompanyID = ind.CompanyID and ity.IndustryID = ind.IndustryID 
left join CodeDefinition cdtitle on city.CompanyID = cdtitle.CompanyID and city.TitleIDCode = cdtitle.Code and GroupCode = 'TITLE' 
LEFT JOIN JobAssignmentUserField jauf on ja.CompanyID = jauf.CompanyID and ja.JobAssignmentID = jauf.JobAssignmentID 
LEFT JOIN ( select ih.CompanyID, id1.SourceID, count( ih.ConfirmedInvoiceNumber ) NumberOfInvoice, max( ih.InvoiceDate ) LastInvoicedDate, sum(id1.TotalInvoicedAmount) TotalInvoicedAmount from InvoiceHead ih 
left join ( select CompanyID, SourceID, UniqueID, sum(Amount) TotalInvoicedAmount from InvoiceDetail where SourceType = 'J' group by CompanyID, SourceID, UniqueID ) id1 
	on ih.CompanyID = id1.CompanyID and ih.UniqueID = id1.UniqueID where ih.Status = 1 group by ih.CompanyID, id1.SourceID ) jis 
	on jis.CompanyID = ja.CompanyID and jis.SourceID = ja.JobAssignmentID WHERE 1=1
		AND (
				CASE 
					WHEN (
						(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasia WHERE (sjasia.JobStatusCode='LOCKED' OR sjasia.JobStatusCode='N_LOCK_CNR' ) AND sjasia.CompanyID = ja.CompanyID AND sjasia.jobAssignmentID=ja.JobAssignmentID
						) IS NOT NULL)
					THEN 
						'LOCKED'
					WHEN (
						(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasib WHERE sjasib.JobStatusCode='LOCKED_F' AND sjasib.CompanyID = ja.CompanyID AND sjasib.jobAssignmentID=ja.JobAssignmentID
						) IS NOT NULL) 
					THEN
						'LOCKED_F'
					WHEN (
						(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasic WHERE sjasic.JobStatusCode='CC' 		AND sjasic.CompanyID = ja.CompanyID AND sjasic.jobAssignmentID=ja.JobAssignmentID
						) IS NOT NULL) 
					THEN
						'CC'
					ELSE
						(select top 1 JobStatusCode from JobAssignmentStatusInfo sjasi where sjasi.JobStatusCode <> '' AND sjasi.CompanyID = ja.CompanyID 	AND sjasi.JobAssignmentID = ja.JobAssignmentID order by sjasi.Date_Update DESC
						)  
				END		
			) <> 'LOCKED'
		AND (jasi.JobStatusCode = 'DOA') 
`;

const get_j58_date_string = (startDate, endDate) => {
  return ` AND (convert(char(10), cast(jasi.Date_Update as date ),20) between '${startDate}' and '${endDate}') `;
};

const j58_orderby_string =
  " Order by JobStatusUpdatedOn , InChargeOfCustomer , JobTeam ";

const sqlcc = `
SELECT DISTINCT 
   cus.InCharge AS InChargeOfCustomerCode ,
	dbo.GetEmployeeNickOrFormatNameByID(cus.InCharge,1,'A') AS InChargeOfCustomer ,    
	rtrim(ja.Team) AS JobTeam , 
	rtrim(ja.CustomerID) AS CustomerID , 
	rtrim(case when cus.CompanyName_A1<>'' then rtrim(cus.CompanyName_A1)+' '+cus.CompanyName_A2 when cus.CompanyName_B1 <>'' then rtrim(cus.CompanyName_B1)+' '+cus.CompanyName_B2 else rtrim(cus.CompanyName_C1)+' '+cus.CompanyName_C2 end) AS CustomerName , 
	rtrim(ja.JobAssignmentID) AS JobAssignmentID , 
	CASE 
		  WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasia WHERE sjasia.JobStatusCode='N_LOCK_CNR' AND sjasia.CompanyID = ja.CompanyID AND sjasia.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		  THEN 
		   'N_LOCK_CNR'
		  WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasia WHERE sjasia.JobStatusCode='LOCKED' AND sjasia.CompanyID = ja.CompanyID AND sjasia.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		  THEN 
		   'LOCKED'
		  WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasib WHERE sjasib.JobStatusCode='LOCKED_F' AND sjasib.CompanyID = ja.CompanyID AND sjasib.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		  THEN
		   'LOCKED_F'
		  WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasic WHERE sjasic.JobStatusCode='CC' AND sjasic.CompanyID = ja.CompanyID AND sjasic.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		  THEN
		   'CC'
		  ELSE
			 (select top 1 JobStatusCode from JobAssignmentStatusInfo sjasi where sjasi.JobStatusCode <> '' AND sjasi.CompanyID = ja.CompanyID and sjasi.JobAssignmentID = ja.JobAssignmentID order by sjasi.Date_Update DESC
			)  
	end AS CurrentJobStatusCode , 
	CAST(( select MAX(Date_Update) from JobAssignmentStatusInfo jasic where jasic.JobAssignmentID = jasi.JobAssignmentID and jasic.JobStatusCode = 'DOA' ) as date) AS jsJobStatusDOADate ,
	rtrim(jasi.JobStatusCode) AS JobStatusCode , 
	cast(jasi.Date_Update as date ) AS JobStatusUpdatedOn , 
	rtrim(cast(ja.CompleteDate as date )) AS JobCompleteDate , 
	dbo.GetEmployeeNickOrFormatNameByID(jasi.EmployeeID,1,'A')  AS JobStatusUpdateBy , 
	case dbo.RBCheckCorrectJobStatusOrder( ja.CompanyID,ja.JobAssignmentID ) when 1 then 'No' else 'Yes' end AS JobStatusOrderCheck , 
	( select COUNT(*) from JobAssignmentStatusInfo jasic where jasic.JobAssignmentID = jasi.JobAssignmentID and jasic.JobStatusCode = jasi.JobStatusCode ) AS jsJobStatusCodeCount , 
	case when ja.Completed = 1 then 'Yes' else 'No' end AS JobCompleted , 
	rtrim(cus.CompanyDefineGroupID) AS cusCompanyDefineGroupID , 
	dbo.GetEmployeeNickOrFormatNameByID(ja.InCharge,1,'A') AS JobIncharge , 
	(select replace(replace(( select distinct '~' + rtrim(ih.ConfirmedInvoiceNumber) + '~' from InvoiceHead ih, 
	InvoiceDetail id where ih.CompanyID = id.CompanyID and ih.UniqueID = id.UniqueID and ih.Status = 1 and id.SourceType = 'J' and id.CompanyID = ja.CompanyID and id.SourceID = ja.JobAssignmentID and isnull( ih.ConfirmedInvoiceNumber, '' ) <> '' order by '~' + rtrim(ih.ConfirmedInvoiceNumber) + '~' for xml path('')),'~~',', '),'~','')) AS JobAssignmentInvoiceList , 
	rtrim(cast(jis.LastInvoicedDate as date)) AS jisLastInvoicedDate , 
	jis.TotalInvoicedAmount AS jisTotalInvoicedAmount ,
	case cus.IsActive when 1 then 'Active' else 'Inactive' end AS CustomerIsActive FROM JobAssignment ja 
	LEFT JOIN JobAssignmentMember jam ON jam.CompanyID = ja.CompanyID AND jam.JobAssignmentID = ja.JobAssignmentID 
	LEFT JOIN Employee e ON jam.CompanyID = e.CompanyID AND jam.EmployeeID = e.EmployeeID 
	LEFT JOIN EmployeeJOb jamj ON e.CompanyID = jamj.CompanyID AND e.EmployeeID = jamj.EmployeeID and ( jamj.Effective = ( SELECT MAX( Effective ) from EmployeeJob jamjs where jamjs.CompanyID = jamj.CompanyID AND jamjs.EmployeeID = jamj.EmployeeID and jamjs.Effective <= GETDATE() )) 
	LEFT JOIN TimeCost jamtc ON jamj.CompanyID = jamtc.CompanyID and jamj.TimeCostID = jamtc.TimeCostID and ( jamtc.Effective = ( SELECT MAX( Effective ) from TimeCost jamtcs where jamtcs.CompanyID = jamtc.CompanyID AND jamtcs.TimeCostID = jamtc.TimeCostID and jamtcs.Effective <= GETDATE() )) 
	LEFT JOIN Contact c ON jam.CompanyID = c.CompanyID AND e.EmployeeID = c.MasterID AND c.MasterType = 'EMP' AND c.SeqID = 1 
	LEFT JOIN Customer cus ON ja.CompanyID = cus.CompanyID AND cus.CustomerID = ja.CustomerID 
	LEFT JOIN Contact cc ON ja.CompanyID = cc.CompanyID AND ja.CustomerID = cc.MasterID AND cc.MasterType = 'CUG' AND cc.SeqID = 1 
	LEFT JOIN JobAssignmentStatusInfo jasi ON ja.CompanyID = jasi.CompanyID and ja.JobAssignmentID = jasi.JobAssignmentID 
	LEFT JOIN Intermediary ity on ja.CompanyID = ity.CompanyID and ja.IntermediaryID = ity.IntermediaryID 
	LEFT JOIN CompanyType ct on ity.CompanyID = ct.CompanyID and ity.CompanyType = ct.CompanyType 
	LEFT JOIN Contact city  on ity.CompanyID = city.CompanyID and ity.IntermediaryID = city.MasterID and city.MasterType = 'REF' 
	LEFT JOIN Industry ind on ity.CompanyID = ind.CompanyID and ity.IndustryID = ind.IndustryID 
	LEFT JOIN CodeDefinition cdtitle on city.CompanyID = cdtitle.CompanyID and city.TitleIDCode = cdtitle.Code and GroupCode = 'TITLE' 
	LEFT JOIN JobAssignmentUserField jauf on ja.CompanyID = jauf.CompanyID and ja.JobAssignmentID = jauf.JobAssignmentID 
	LEFT JOIN ( select ih.CompanyID, id1.SourceID, count( ih.ConfirmedInvoiceNumber ) NumberOfInvoice, max( ih.InvoiceDate ) LastInvoicedDate, sum(id1.TotalInvoicedAmount) TotalInvoicedAmount from InvoiceHead ih 
	LEFT JOIN ( select CompanyID, SourceID, UniqueID, sum(Amount) TotalInvoicedAmount from InvoiceDetail where SourceType = 'J' group by CompanyID, SourceID, UniqueID ) id1 on ih.CompanyID = id1.CompanyID and ih.UniqueID = id1.UniqueID where ih.Status = 1 group by ih.CompanyID, id1.SourceID ) jis on jis.CompanyID = ja.CompanyID and jis.SourceID = ja.JobAssignmentID 
		WHERE 1=1
		AND (
			CASE 
				WHEN (
					(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasia WHERE (sjasia.JobStatusCode='LOCKED' OR sjasia.JobStatusCode='N_LOCK_CNR') AND sjasia.CompanyID = ja.CompanyID AND sjasia.jobAssignmentID=ja.JobAssignmentID
					) IS NOT NULL)
				THEN 
					'LOCKED'
				WHEN (
					(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasib WHERE sjasib.JobStatusCode='LOCKED_F' AND sjasib.CompanyID = ja.CompanyID AND sjasib.jobAssignmentID=ja.JobAssignmentID
					) IS NOT NULL) 
				THEN
					'LOCKED_F'
				WHEN (
						(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasic WHERE sjasic.JobStatusCode='CC' 		AND sjasic.CompanyID = ja.CompanyID AND sjasic.jobAssignmentID=ja.JobAssignmentID
					) IS NOT NULL) 
				THEN
					'CC'
				ELSE
					(select top 1 JobStatusCode from JobAssignmentStatusInfo sjasi where sjasi.JobStatusCode <> '' AND sjasi.CompanyID = ja.CompanyID 	AND sjasi.JobAssignmentID = ja.JobAssignmentID order by sjasi.Date_Update DESC
					)  
				END		
			) <> 'LOCKED'		   
		AND (jasi.JobStatusCode = 'CC') 
		AND 
			(
			(ja.BillingCode = 'ACC-ACC') OR (ja.BillingCode = 'AUD-IA') OR (ja.BillingCode = 'AUD-IAL') OR (ja.BillingCode like 'AUD-SA_') 
			OR (ja.BillingCode = 'AUD-SR') OR (ja.BillingCode = 'SPE-NT') OR (ja.BillingCode = 'SPE-DD') OR (ja.BillingCode = 'SPE-OS') OR (ja.BillingCode = 'SPE-PGT') 
			OR (ja.BillingCode = 'SPE-SR') OR (ja.BillingCode = 'SPE-ICR') OR (ja.BillingCode = 'SPE-MAR') OR (ja.BillingCode = 'SPE-PRC') 
			)
		AND 
			(
			(ja.CustomerID NOT LIKE '%K') OR (ja.CustomerID LIKE '%SK')
			)
`;

const sqlcc_all = `
SELECT DISTINCT 
	cus.InCharge AS InChargeOfCustomerCode ,
	dbo.GetEmployeeNickOrFormatNameByID(cus.InCharge,1,'A') AS InChargeOfCustomer , 
    rtrim(ja.Team) AS JobTeam , 
	rtrim(ja.CustomerID) AS CustomerID , 
	rtrim(case when cus.CompanyName_A1<>'' then rtrim(cus.CompanyName_A1)+' '+cus.CompanyName_A2 when cus.CompanyName_B1 <>'' then rtrim(cus.CompanyName_B1)+' '+cus.CompanyName_B2 else rtrim(cus.CompanyName_C1)+' '+cus.CompanyName_C2 end) AS CustomerName , 
	rtrim(ja.JobAssignmentID) AS JobAssignmentID , 
	CASE 
		WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasia WHERE sjasia.JobStatusCode='N_LOCK_CNR' AND sjasia.CompanyID = ja.CompanyID AND sjasia.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		THEN 
		  	'N_LOCK_CNR'
		WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasia WHERE sjasia.JobStatusCode='LOCKED' AND sjasia.CompanyID = ja.CompanyID AND sjasia.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		THEN 
		  	'LOCKED'
		WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasib WHERE sjasib.JobStatusCode='LOCKED_F' AND sjasib.CompanyID = ja.CompanyID AND sjasib.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		THEN
		   	'LOCKED_F'
		WHEN (
			(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasic WHERE sjasic.JobStatusCode='CC' AND sjasic.CompanyID = ja.CompanyID AND sjasic.jobAssignmentID=ja.JobAssignmentID
			)	IS NOT NULL) 
		THEN
		   'CC'
		ELSE
			 (select top 1 JobStatusCode from JobAssignmentStatusInfo sjasi where sjasi.JobStatusCode <> '' AND sjasi.CompanyID = ja.CompanyID and sjasi.JobAssignmentID = ja.JobAssignmentID order by sjasi.Date_Update DESC
			)  
	end AS CurrentJobStatusCode , 
	CAST(( select MAX(Date_Update) from JobAssignmentStatusInfo jasic where jasic.JobAssignmentID = jasi.JobAssignmentID and jasic.JobStatusCode = 'DOA' ) as date) AS jsJobStatusDOADate ,
	rtrim(jasi.JobStatusCode) AS JobStatusCode , 
	cast(jasi.Date_Update as date ) AS JobStatusUpdatedOn , 
	rtrim(cast(ja.CompleteDate as date )) AS JobCompleteDate , 
	dbo.GetEmployeeNickOrFormatNameByID(jasi.EmployeeID,1,'A')  AS JobStatusUpdateBy , 
	case dbo.RBCheckCorrectJobStatusOrder( ja.CompanyID,ja.JobAssignmentID ) when 1 then 'No' else 'Yes' end AS JobStatusOrderCheck , 
	( select COUNT(*) from JobAssignmentStatusInfo jasic where jasic.JobAssignmentID = jasi.JobAssignmentID and jasic.JobStatusCode = jasi.JobStatusCode ) AS jsJobStatusCodeCount , 
    case when ja.Completed = 1 then 'Yes' else 'No' end AS JobCompleted , 
    rtrim(cus.CompanyDefineGroupID) AS cusCompanyDefineGroupID , 
	dbo.GetEmployeeNickOrFormatNameByID(ja.InCharge,1,'A') AS JobIncharge , 
	(select replace(replace(( select distinct '~' + rtrim(ih.ConfirmedInvoiceNumber) + '~' from InvoiceHead ih, 
	InvoiceDetail id where ih.CompanyID = id.CompanyID and ih.UniqueID = id.UniqueID and ih.Status = 1 and id.SourceType = 'J' and id.CompanyID = ja.CompanyID and id.SourceID = ja.JobAssignmentID and isnull( ih.ConfirmedInvoiceNumber, '' ) <> '' order by '~' + rtrim(ih.ConfirmedInvoiceNumber) + '~' for xml path('')),'~~',', '),'~','')) AS JobAssignmentInvoiceList , 
	rtrim(cast(jis.LastInvoicedDate as date)) AS jisLastInvoicedDate , 
	jis.TotalInvoicedAmount AS jisTotalInvoicedAmount ,
	case cus.IsActive when 1 then 'Active' else 'Inactive' end AS CustomerIsActive FROM JobAssignment ja 
	LEFT JOIN JobAssignmentMember jam ON jam.CompanyID = ja.CompanyID AND jam.JobAssignmentID = ja.JobAssignmentID 
	LEFT JOIN Employee e ON jam.CompanyID = e.CompanyID AND jam.EmployeeID = e.EmployeeID 
	LEFT JOIN EmployeeJOb jamj ON e.CompanyID = jamj.CompanyID AND e.EmployeeID = jamj.EmployeeID and ( jamj.Effective = ( SELECT MAX( Effective ) from EmployeeJob jamjs where jamjs.CompanyID = jamj.CompanyID AND jamjs.EmployeeID = jamj.EmployeeID and jamjs.Effective <= GETDATE() )) 
	LEFT JOIN TimeCost jamtc ON jamj.CompanyID = jamtc.CompanyID and jamj.TimeCostID = jamtc.TimeCostID and ( jamtc.Effective = ( SELECT MAX( Effective ) from TimeCost jamtcs where jamtcs.CompanyID = jamtc.CompanyID AND jamtcs.TimeCostID = jamtc.TimeCostID and jamtcs.Effective <= GETDATE() )) 
	LEFT JOIN Contact c ON jam.CompanyID = c.CompanyID AND e.EmployeeID = c.MasterID AND c.MasterType = 'EMP' AND c.SeqID = 1 
	LEFT JOIN Customer cus ON ja.CompanyID = cus.CompanyID AND cus.CustomerID = ja.CustomerID 
	LEFT JOIN Contact cc ON ja.CompanyID = cc.CompanyID AND ja.CustomerID = cc.MasterID AND cc.MasterType = 'CUG' AND cc.SeqID = 1 
	LEFT JOIN JobAssignmentStatusInfo jasi ON ja.CompanyID = jasi.CompanyID and ja.JobAssignmentID = jasi.JobAssignmentID 
	LEFT JOIN Intermediary ity on ja.CompanyID = ity.CompanyID and ja.IntermediaryID = ity.IntermediaryID 
	LEFT JOIN CompanyType ct on ity.CompanyID = ct.CompanyID and ity.CompanyType = ct.CompanyType 
	LEFT JOIN Contact city  on ity.CompanyID = city.CompanyID and ity.IntermediaryID = city.MasterID and city.MasterType = 'REF' 
	LEFT JOIN Industry ind on ity.CompanyID = ind.CompanyID and ity.IndustryID = ind.IndustryID 
	LEFT JOIN CodeDefinition cdtitle on city.CompanyID = cdtitle.CompanyID and city.TitleIDCode = cdtitle.Code and GroupCode = 'TITLE' 
	LEFT JOIN JobAssignmentUserField jauf on ja.CompanyID = jauf.CompanyID and ja.JobAssignmentID = jauf.JobAssignmentID 
	LEFT JOIN ( select ih.CompanyID, id1.SourceID, count( ih.ConfirmedInvoiceNumber ) NumberOfInvoice, max( ih.InvoiceDate ) LastInvoicedDate, sum(id1.TotalInvoicedAmount) TotalInvoicedAmount from InvoiceHead ih 
	LEFT JOIN ( select CompanyID, SourceID, UniqueID, sum(Amount) TotalInvoicedAmount from InvoiceDetail where SourceType = 'J' group by CompanyID, SourceID, UniqueID ) id1 on ih.CompanyID = id1.CompanyID and ih.UniqueID = id1.UniqueID where ih.Status = 1 group by ih.CompanyID, id1.SourceID ) jis on jis.CompanyID = ja.CompanyID and jis.SourceID = ja.JobAssignmentID 
		WHERE 1=1
		AND (
				CASE 
					WHEN (
						(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasia WHERE (sjasia.JobStatusCode='LOCKED' OR sjasia.JobStatusCode='N_LOCK_CNR' ) AND sjasia.CompanyID = ja.CompanyID AND sjasia.jobAssignmentID=ja.JobAssignmentID
						) IS NOT NULL)
					THEN 
						'LOCKED'
					WHEN (
						(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasib WHERE sjasib.JobStatusCode='LOCKED_F' AND sjasib.CompanyID = ja.CompanyID AND sjasib.jobAssignmentID=ja.JobAssignmentID
						) IS NOT NULL) 
					THEN
						'LOCKED_F'
					WHEN (
						(select TOP 1 JobStatusCode from JobAssignmentStatusInfo sjasic WHERE sjasic.JobStatusCode='CC' 		AND sjasic.CompanyID = ja.CompanyID AND sjasic.jobAssignmentID=ja.JobAssignmentID
						) IS NOT NULL) 
					THEN
						'CC'
					ELSE
						(select top 1 JobStatusCode from JobAssignmentStatusInfo sjasi where sjasi.JobStatusCode <> '' AND sjasi.CompanyID = ja.CompanyID 	AND sjasi.JobAssignmentID = ja.JobAssignmentID order by sjasi.Date_Update DESC
						)  
				END		
			) <> 'LOCKED'		   
		AND (jasi.JobStatusCode = 'CC') 
`;

const get_date_string = (startDate, endDate) => {
  return `AND( convert(char(10), cast(jasi.Date_Update as date ),20) between '${startDate}' and '${endDate}') `;
};
const cc_orderby_string =
  "Order by InChargeOfCustomerCode , JobTeam , JobIncharge";

const ChargeHour_string = `
SELECT 
	a.Team,
	a.JobID, 
	a.CustomerID,
	a.CompanyName,
	a.Incharge,
	a.Assistant,
	a.TotalWorkHour, 
 	CASE WHEN (a.FirstWorkDate IS NULL) THEN CAST('1900-01-01' AS date) ELSE a.FirstWorkDate END AS FirstWorkDate,
 	CASE WHEN (a.DOA_Date IS NULL) THEN CAST('1900-01-01' AS date) ELSE a.DOA_Date END AS DOA_Date,
 	CASE WHEN (a.LOCKED_Date IS NULL) THEN CAST('1900-01-01' AS date) ELSE a.LOCKED_Date END AS LOCKED_Date,
	CASE WHEN (DATEDIFF(day,a.FirstWorkDate,a.DOA_Date) IS NULL) THEN -99999 ELSE DATEDIFF(day,a.FirstWorkDate,a.DOA_Date) END AS FirstWorkDate_Days,
	CASE WHEN (DATEDIFF(day,a.DOA_Date,a.LOCKED_Date) IS NULL) THEN -99999 ELSE DATEDIFF(day,a.DOA_Date,a.LOCKED_Date) END AS Locked_Days
	FROM
	(SELECT 
   		rtrim(ja.JobAssignmentID) AS JobID, 
	   	rtrim(ja.Team) AS Team,
   		rtrim(ja.CustomerID) AS CustomerID , 
	   	rtrim(cus.CompanyName_A1)  AS CompanyName,
	   	dbo.GetEmployeeNickOrFormatNameByID(ja.InCharge,1,'A')  AS Incharge,
	   	dbo.GetEmployeeNickOrFormatNameByID(ja.Assistant,1,'A') AS Assistant,
	   	cast(MIN(t.WorkDate) as date ) AS FirstWorkDate ,   
	   	SUM(tw.WorkHour ) AS TotalWorkHour,
   	   	cast((select MIN(d.Date_Update)  FROM JobAssignmentStatusInfo d WHERE d.JobStatusCode='DOA' AND ja.JobAssignmentID=d.JobAssignmentID) as date ) AS DOA_Date,
	   	cast((select MIN(f.Date_Update)  FROM JobAssignmentStatusInfo f WHERE f.JobStatusCode='LOCKED' AND ja.JobAssignmentID=f.JobAssignmentID) as date ) AS LOCKED_Date
 		FROM TimeSheet t 
			LEFT JOIN Timesheetwork tw  ON t.UniqueID = tw.UniqueID AND t.CompanyID = 'CHENG'
			LEFT JOIN JobAssignment ja ON t.JobAssignmentID = ja.JobAssignmentID
			LEFT JOIN Customer cus ON ja.CustomerID = cus.CustomerID
			where 1=1
			AND 
			(
			(ja.BillingCode = 'ACC-ACC') OR (ja.BillingCode = 'AUD-IA') OR (ja.BillingCode = 'AUD-IAL') OR (ja.BillingCode like 'AUD-SA_') 
			OR (ja.BillingCode = 'AUD-SR') OR (ja.BillingCode = 'SPE-NT') OR (ja.BillingCode = 'SPE-DD') OR (ja.BillingCode = 'SPE-OS') OR (ja.BillingCode = 'SPE-PGT') 
			OR (ja.BillingCode = 'SPE-SR') OR (ja.BillingCode = 'SPE-ICR') OR (ja.BillingCode = 'SPE-MAR') OR (ja.BillingCode = 'SPE-PRC') 
			)
			AND cus.IsActive = 1
			GROUP BY ja.JobAssignmentID, ja.Team, ja.CustomerID, ja.Incharge, ja.Assistant, cus.CompanyName_A1
	) a WHERE 1=1 
	AND a.TotalWorkHour > 0
`;

const get_ChargeHour_date = (startDate, endDate) => {
  return `AND cast(a.FirstWorkDate as date) BETWEEN '${startDate}' AND '${endDate}' `;
};

const ChargeHour_orderby_string = "ORDER BY FirstWorkDate ASC";

const Incorrectlocked_string = `
SELECT DISTINCT  
cast(jasi.Date_Update as date ) AS Date_Update,
jasi.JobAssignmentID AS Job_ID, 
dbo.GetEmployeeNickOrFormatNameByID(EmployeeID,1,'A') AS Employee_Name
from JobAssignmentStatusInfo jasi 
WHERE 1=1
AND (jasi.JobStatusCode = 'LOCKED' OR jasi.JobStatusCode = 'N_LOCK_CNR') and (jasi.EmployeeID <> 'DL013' AND jasi.EmployeeID <> 'VC001' AND jasi.EmployeeID <> 'AL019')
`;

const get_Incorrectlocked_date = (startDate, endDate) => {
  return `AND cast(jasi.Date_Update as date) BETWEEN '${startDate}' AND '${endDate}' `;
};

const Incorrectlocked_orderby_string = "ORDER BY Date_Update DESC, Job_ID";

app.use(bodyParser.json());

const sequelize = new Sequelize("cheng3_1", "sa", "pl123098", {
  host: "*************",
  dialect: "mssql",
  dialectOptions: {
    options: {
      encrypt: false,
      //      trustServerCertificate: false,
      loginTimeout: 30,
      requestTimeout: 60000, //  query expiry 60s
      validateBulkLoadParameters: true,
    },
  },
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
});

// sequelize
//   .authenticate()
//   .then(function (success) {
//     console.log("Successfully we are connected");
//   })
//   .catch(function (error) {
//     console.log("Error!");
//   });

app.get("/", function (req, res) {
  res.status(200).json({
    status: 1,
    message: "Welcome to Home page",
  });
});

app.get("/jobcc", function (req, res) {
  var startDate = req.query.startDate;
  var endDate = req.query.endDate;
  sequelize
    .query(sqlcc + get_date_string(startDate, endDate) + cc_orderby_string, {
      type: Sequelize.QueryTypes.SELECT,
    })
    .then((response) => {
      res.status(200).json({
        status: 1,
        message: "Query Sccuess",
        data: response,
      });
    })
    .catch((error) => {
      res.status(200).json({
        status: 0,
        message: "Query Error",
        data: error,
      });
    });
});

app.get("/job58", function (req, res) {
  var startDate = req.query.startDate;
  var endDate = req.query.endDate;
  sequelize
    .query(
      sql58 + get_j58_date_string(startDate, endDate) + j58_orderby_string,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    )
    .then((response) => {
      res.status(200).json({
        status: 1,
        message: "Query Sccuess",
        data: response,
      });
    })
    .catch((error) => {
      res.status(200).json({
        status: 0,
        message: "Query Error",
        data: error,
      });
    });
});

app.get("/jobccall", function (req, res) {
  var startDate = req.query.startDate;
  var endDate = req.query.endDate;
  sequelize
    .query(
      sqlcc_all + get_date_string(startDate, endDate) + cc_orderby_string,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    )
    .then((response) => {
      res.status(200).json({
        status: 1,
        message: "Query Sccuess",
        data: response,
      });
    })
    .catch((error) => {
      res.status(200).json({
        status: 0,
        message: "Query Error",
        data: error,
      });
    });
});

app.get("/job58all", function (req, res) {
  var startDate = req.query.startDate;
  var endDate = req.query.endDate;
  sequelize
    .query(
      sql58_all + get_j58_date_string(startDate, endDate) + j58_orderby_string,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    )
    .then((response) => {
      res.status(200).json({
        status: 1,
        message: "Query Sccuess",
        data: response,
      });
    })
    .catch((error) => {
      res.status(200).json({
        status: 0,
        message: "Query Error",
        data: error,
      });
    });
});

app.get("/chargehour", function (req, res) {
  var startDate = req.query.startDate;
  var endDate = req.query.endDate;
  sequelize
    .query(
      ChargeHour_string +
        get_ChargeHour_date(startDate, endDate) +
        ChargeHour_orderby_string,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    )
    .then((response) => {
      res.status(200).json({
        status: 1,
        message: "Query Sccuess",
        data: response,
      });
    })
    .catch((error) => {
      res.status(200).json({
        status: 0,
        message: "Query Error",
        data: error,
      });
    });
});

app.get("/incorrectlocked", function (req, res) {
  var startDate = req.query.startDate;
  var endDate = req.query.endDate;
  sequelize
    .query(
      Incorrectlocked_string +
        get_Incorrectlocked_date(startDate, endDate) +
        Incorrectlocked_orderby_string,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    )
    .then((response) => {
      res.status(200).json({
        status: 1,
        message: "Query Sccuess",
        data: response,
      });
    })
    .catch((error) => {
      res.status(200).json({
        status: 0,
        message: "Query Error",
        data: error,
      });
    });
});
// raw query to select data

app.listen(PORT, function () {
  console.log("Application is running");
});
